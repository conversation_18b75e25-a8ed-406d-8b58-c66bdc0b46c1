package handlers

import (
	"net/http"

	"github.com/labstack/echo/v4"

	"backend/internal/errors"
	"backend/internal/models"
	"backend/internal/service"
)

// TodoHandler handles HTTP requests for todo operations
type TodoHandler struct {
	service service.TodoService
}

// NewTodoHandler creates a new todo handler
func NewTodoHandler(service service.TodoService) *TodoHandler {
	return &TodoHandler{
		service: service,
	}
}

// CreateTodo handles POST /todos
func (h *TodoHandler) CreateTodo(c echo.Context) error {
	var req models.CreateTodoRequest
	if err := c.Bind(&req); err != nil {
		return h.handleError(c, errors.NewBadRequestError("invalid request body"))
	}

	todo, err := h.service.CreateTodo(c.Request().Context(), &req)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.<PERSON>SON(http.StatusCreated, todo)
}

// GetTodo handles GET /todos/{id}
func (h *TodoHandler) GetTodo(c echo.Context) error {
	id := c.Param("id")
	if id == "" {
		return h.handleError(c, errors.NewBadRequestError("todo ID is required"))
	}

	todo, err := h.service.GetTodoByID(c.Request().Context(), id)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, todo)
}

// GetAllTodos handles GET /todos
func (h *TodoHandler) GetAllTodos(c echo.Context) error {
	todos, err := h.service.GetAllTodos(c.Request().Context())
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"todos": todos,
		"count": len(todos),
	})
}

// UpdateTodo handles PUT /todos/{id}
func (h *TodoHandler) UpdateTodo(c echo.Context) error {
	id := c.Param("id")
	if id == "" {
		return h.handleError(c, errors.NewBadRequestError("todo ID is required"))
	}

	var req models.UpdateTodoRequest
	if err := c.Bind(&req); err != nil {
		return h.handleError(c, errors.NewBadRequestError("invalid request body"))
	}

	todo, err := h.service.UpdateTodo(c.Request().Context(), id, &req)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, todo)
}

// DeleteTodo handles DELETE /todos/{id}
func (h *TodoHandler) DeleteTodo(c echo.Context) error {
	id := c.Param("id")
	if id == "" {
		return h.handleError(c, errors.NewBadRequestError("todo ID is required"))
	}

	err := h.service.DeleteTodo(c.Request().Context(), id)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "todo deleted successfully",
	})
}

// handleError handles application errors and returns appropriate HTTP responses
func (h *TodoHandler) handleError(c echo.Context, err error) error {
	if appErr, ok := err.(*errors.AppError); ok {
		return c.JSON(appErr.Code, map[string]interface{}{
			"error":   true,
			"message": appErr.Message,
		})
	}

	// Handle unexpected errors
	return c.JSON(http.StatusInternalServerError, map[string]interface{}{
		"error":   true,
		"message": "internal server error",
	})
}
