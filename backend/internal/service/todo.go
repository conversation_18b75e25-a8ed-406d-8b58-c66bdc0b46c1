package service

import (
	"context"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"

	"backend/internal/errors"
	"backend/internal/models"
	"backend/internal/repository"
)

// TodoService defines the interface for todo business logic
type TodoService interface {
	CreateTodo(ctx context.Context, req *models.CreateTodoRequest) (*models.Todo, error)
	GetTodoByID(ctx context.Context, id string) (*models.Todo, error)
	GetAllTodos(ctx context.Context) ([]*models.Todo, error)
	UpdateTodo(ctx context.Context, id string, req *models.UpdateTodoRequest) (*models.Todo, error)
	DeleteTodo(ctx context.Context, id string) error
}

// todoService implements TodoService
type todoService struct {
	repo      repository.TodoRepository
	validator *validator.Validate
}

// NewTodoService creates a new todo service
func NewTodoService(repo repository.TodoRepository) TodoService {
	return &todoService{
		repo:      repo,
		validator: validator.New(),
	}
}

// CreateTodo creates a new todo
func (s *todoService) CreateTodo(ctx context.Context, req *models.CreateTodoRequest) (*models.Todo, error) {
	// Validate request
	if err := s.validator.Struct(req); err != nil {
		return nil, errors.NewValidationError("validation failed: " + err.Error())
	}

	// Trim whitespace
	req.Title = strings.TrimSpace(req.Title)
	req.Description = strings.TrimSpace(req.Description)

	// Create new todo
	todo := models.NewTodo(req.Title, req.Description)

	// Save to repository
	if err := s.repo.Create(ctx, todo); err != nil {
		return nil, err
	}

	return todo, nil
}

// GetTodoByID retrieves a todo by ID
func (s *todoService) GetTodoByID(ctx context.Context, id string) (*models.Todo, error) {
	// Validate ID format
	if !s.isValidUUID(id) {
		return nil, errors.ErrInvalidID
	}

	return s.repo.GetByID(ctx, id)
}

// GetAllTodos retrieves all todos
func (s *todoService) GetAllTodos(ctx context.Context) ([]*models.Todo, error) {
	return s.repo.GetAll(ctx)
}

// UpdateTodo updates an existing todo
func (s *todoService) UpdateTodo(ctx context.Context, id string, req *models.UpdateTodoRequest) (*models.Todo, error) {
	// Validate ID format
	if !s.isValidUUID(id) {
		return nil, errors.ErrInvalidID
	}

	// Validate request
	if err := s.validator.Struct(req); err != nil {
		return nil, errors.NewValidationError("validation failed: " + err.Error())
	}

	// Get existing todo
	todo, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Trim whitespace from string fields
	if req.Title != nil {
		trimmed := strings.TrimSpace(*req.Title)
		req.Title = &trimmed
	}
	if req.Description != nil {
		trimmed := strings.TrimSpace(*req.Description)
		req.Description = &trimmed
	}

	// Update todo
	todo.Update(req)

	// Save updated todo
	if err := s.repo.Update(ctx, todo); err != nil {
		return nil, err
	}

	return todo, nil
}

// DeleteTodo deletes a todo by ID
func (s *todoService) DeleteTodo(ctx context.Context, id string) error {
	// Validate ID format
	if !s.isValidUUID(id) {
		return errors.ErrInvalidID
	}

	return s.repo.Delete(ctx, id)
}

// isValidUUID checks if the provided string is a valid UUID
func (s *todoService) isValidUUID(id string) bool {
	_, err := uuid.Parse(id)
	return err == nil
}
