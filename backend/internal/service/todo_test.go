package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"backend/internal/models"
	"backend/test/testutil"
)

func TestTodoService_CreateTodo(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	req := &models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}

	todo, err := service.CreateTodo(ctx, req)
	require.NoError(t, err)
	assert.NotEmpty(t, todo.ID)
	assert.Equal(t, req.Title, todo.Title)
	assert.Equal(t, req.Description, todo.Description)
	assert.False(t, todo.Completed)
}

func TestTodoService_CreateTodoValidation(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	tests := []struct {
		name    string
		request *models.CreateTodoRequest
		wantErr bool
	}{
		{
			name: "valid request",
			request: &models.CreateTodoRequest{
				Title:       "Valid Title",
				Description: "Valid Description",
			},
			wantErr: false,
		},
		{
			name: "empty title",
			request: &models.CreateTodoRequest{
				Title:       "",
				Description: "Valid Description",
			},
			wantErr: true,
		},
		{
			name: "title too long",
			request: &models.CreateTodoRequest{
				Title:       string(make([]byte, 201)), // 201 characters
				Description: "Valid Description",
			},
			wantErr: true,
		},
		{
			name: "description too long",
			request: &models.CreateTodoRequest{
				Title:       "Valid Title",
				Description: string(make([]byte, 1001)), // 1001 characters
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := service.CreateTodo(ctx, tt.request)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestTodoService_CreateTodoTrimWhitespace(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	req := &models.CreateTodoRequest{
		Title:       "  Test Todo  ",
		Description: "  Test Description  ",
	}

	todo, err := service.CreateTodo(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, "Test Todo", todo.Title)
	assert.Equal(t, "Test Description", todo.Description)
}

func TestTodoService_GetTodoByID(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	// Create a todo first
	createReq := &models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}
	created, err := service.CreateTodo(ctx, createReq)
	require.NoError(t, err)

	// Get the todo
	retrieved, err := service.GetTodoByID(ctx, created.ID)
	require.NoError(t, err)
	assert.Equal(t, created.ID, retrieved.ID)
	assert.Equal(t, created.Title, retrieved.Title)
}

func TestTodoService_GetTodoByIDInvalidID(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	_, err := service.GetTodoByID(ctx, "invalid-uuid")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid todo ID")
}

func TestTodoService_UpdateTodo(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	// Create a todo first
	createReq := &models.CreateTodoRequest{
		Title:       "Original Title",
		Description: "Original Description",
	}
	created, err := service.CreateTodo(ctx, createReq)
	require.NoError(t, err)

	// Update the todo
	newTitle := "Updated Title"
	completed := true
	updateReq := &models.UpdateTodoRequest{
		Title:     &newTitle,
		Completed: &completed,
	}

	updated, err := service.UpdateTodo(ctx, created.ID, updateReq)
	require.NoError(t, err)
	assert.Equal(t, newTitle, updated.Title)
	assert.Equal(t, created.Description, updated.Description) // Should remain unchanged
	assert.True(t, updated.Completed)
}

func TestTodoService_DeleteTodo(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	// Create a todo first
	createReq := &models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}
	created, err := service.CreateTodo(ctx, createReq)
	require.NoError(t, err)

	// Delete the todo
	err = service.DeleteTodo(ctx, created.ID)
	require.NoError(t, err)

	// Verify deletion
	_, err = service.GetTodoByID(ctx, created.ID)
	assert.Error(t, err)
}
