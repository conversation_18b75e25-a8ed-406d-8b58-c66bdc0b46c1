package repository

import (
	"context"
	"log"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"backend/internal/config"
	"backend/internal/database"
	"backend/internal/errors"
	"backend/internal/migrations"
	"backend/internal/models"
)

// PostgresTodoRepositoryTestSuite defines the test suite for PostgreSQL repository
type PostgresTodoRepositoryTestSuite struct {
	suite.Suite
	db   *database.PostgresDB
	repo *PostgresTodoRepository
	pool *pgxpool.Pool
}

// SetupSuite runs once before all tests
func (suite *PostgresTodoRepositoryTestSuite) SetupSuite() {
	// Load .env file for test configuration
	// Try multiple paths to find the .env file
	envPaths := []string{".env", "../.env", "../../.env", "../../../.env"}
	var envLoaded bool
	for _, path := range envPaths {
		if err := godotenv.Load(path); err == nil {
			envLoaded = true
			break
		}
	}
	if !envLoaded {
		log.Printf("Warning: Could not load .env file for tests from any of the paths: %v", envPaths)
	}

	// Use test database configuration
	cfg := &config.DatabaseConfig{
		Host:           getEnvOrDefault("DB_HOST", "localhost"),
		Port:           getEnvAsInt("DB_PORT", 5432),
		Name:           getEnvOrDefault("DB_NAME", "todo_test"),
		User:           getEnvOrDefault("DB_USER", "postgres"),
		Password:       getEnvOrDefault("DB_PASSWORD", ""),
		SSLMode:        getEnvOrDefault("DB_SSLMODE", "prefer"),
		MaxConns:       getEnvAsInt("DB_MAX_CONNS", 5),
		MinConns:       getEnvAsInt("DB_MIN_CONNS", 1),
		ConnectTimeout: getEnvAsDuration("DB_CONNECT_TIMEOUT", 5*time.Second),  // Shorter timeout for tests
		QueryTimeout:   getEnvAsDuration("DB_QUERY_TIMEOUT", 10*time.Second),  // Shorter timeout for tests
	}

	// Create database connection
	db, err := database.NewPostgresDB(cfg)
	require.NoError(suite.T(), err, "Failed to connect to test database")

	suite.db = db
	suite.pool = db.Pool

	// Run migrations
	migrator := migrations.NewMigrator(suite.pool)
	err = migrator.Up(context.Background())
	require.NoError(suite.T(), err, "Failed to run migrations")

	// Create repository
	suite.repo = NewPostgresTodoRepository(suite.pool)
}

// TearDownSuite runs once after all tests
func (suite *PostgresTodoRepositoryTestSuite) TearDownSuite() {
	if suite.db != nil {
		suite.db.Close()
	}
}

// SetupTest runs before each test
func (suite *PostgresTodoRepositoryTestSuite) SetupTest() {
	// Clean up todos table before each test
	_, err := suite.pool.Exec(context.Background(), "DELETE FROM todos")
	require.NoError(suite.T(), err, "Failed to clean up todos table")
}

// TestCreate tests the Create method
func (suite *PostgresTodoRepositoryTestSuite) TestCreate() {
	ctx := context.Background()
	todo := models.NewTodo("Test Todo", "Test Description")

	err := suite.repo.Create(ctx, todo)
	assert.NoError(suite.T(), err)

	// Verify todo was created
	retrieved, err := suite.repo.GetByID(ctx, todo.ID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), todo.ID, retrieved.ID)
	assert.Equal(suite.T(), todo.Title, retrieved.Title)
	assert.Equal(suite.T(), todo.Description, retrieved.Description)
	assert.Equal(suite.T(), todo.Completed, retrieved.Completed)
}

// TestCreateDuplicate tests creating a todo with duplicate ID
func (suite *PostgresTodoRepositoryTestSuite) TestCreateDuplicate() {
	ctx := context.Background()
	todo := models.NewTodo("Test Todo", "Test Description")

	// Create first time - should succeed
	err := suite.repo.Create(ctx, todo)
	require.NoError(suite.T(), err)

	// Create same todo again - should fail
	err = suite.repo.Create(ctx, todo)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "already exists")
}

// TestGetByID tests the GetByID method
func (suite *PostgresTodoRepositoryTestSuite) TestGetByID() {
	ctx := context.Background()
	todo := models.NewTodo("Test Todo", "Test Description")

	// Create todo first
	err := suite.repo.Create(ctx, todo)
	require.NoError(suite.T(), err)

	// Get existing todo
	retrieved, err := suite.repo.GetByID(ctx, todo.ID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), todo.ID, retrieved.ID)
	assert.Equal(suite.T(), todo.Title, retrieved.Title)

	// Get non-existing todo (use valid UUID format)
	_, err = suite.repo.GetByID(ctx, "550e8400-e29b-41d4-a716-************")
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), errors.ErrTodoNotFound, err)
}

// TestGetAll tests the GetAll method
func (suite *PostgresTodoRepositoryTestSuite) TestGetAll() {
	ctx := context.Background()

	// Initially empty
	todos, err := suite.repo.GetAll(ctx)
	require.NoError(suite.T(), err)
	assert.Empty(suite.T(), todos)

	// Add some todos
	todo1 := models.NewTodo("Todo 1", "Description 1")
	todo2 := models.NewTodo("Todo 2", "Description 2")

	err = suite.repo.Create(ctx, todo1)
	require.NoError(suite.T(), err)
	err = suite.repo.Create(ctx, todo2)
	require.NoError(suite.T(), err)

	// Get all todos
	todos, err = suite.repo.GetAll(ctx)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), todos, 2)

	// Verify todos are returned (should be ordered by created_at DESC)
	assert.Equal(suite.T(), todo2.ID, todos[0].ID) // Most recent first
	assert.Equal(suite.T(), todo1.ID, todos[1].ID)
}

// TestUpdate tests the Update method
func (suite *PostgresTodoRepositoryTestSuite) TestUpdate() {
	ctx := context.Background()
	todo := models.NewTodo("Original Title", "Original Description")

	// Create todo first
	err := suite.repo.Create(ctx, todo)
	require.NoError(suite.T(), err)

	// Update todo
	todo.Title = "Updated Title"
	todo.Completed = true
	err = suite.repo.Update(ctx, todo)
	require.NoError(suite.T(), err)

	// Verify update
	updated, err := suite.repo.GetByID(ctx, todo.ID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Updated Title", updated.Title)
	assert.True(suite.T(), updated.Completed)
}

// TestUpdateNonExisting tests updating a non-existing todo
func (suite *PostgresTodoRepositoryTestSuite) TestUpdateNonExisting() {
	ctx := context.Background()
	todo := models.NewTodo("Test Todo", "Test Description")

	// Try to update non-existing todo
	err := suite.repo.Update(ctx, todo)
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), errors.ErrTodoNotFound, err)
}

// TestDelete tests the Delete method
func (suite *PostgresTodoRepositoryTestSuite) TestDelete() {
	ctx := context.Background()
	todo := models.NewTodo("Test Todo", "Test Description")

	// Create todo first
	err := suite.repo.Create(ctx, todo)
	require.NoError(suite.T(), err)

	// Delete todo
	err = suite.repo.Delete(ctx, todo.ID)
	require.NoError(suite.T(), err)

	// Verify deletion
	_, err = suite.repo.GetByID(ctx, todo.ID)
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), errors.ErrTodoNotFound, err)
}

// TestDeleteNonExisting tests deleting a non-existing todo
func (suite *PostgresTodoRepositoryTestSuite) TestDeleteNonExisting() {
	ctx := context.Background()

	// Try to delete non-existing todo (use valid UUID format)
	err := suite.repo.Delete(ctx, "550e8400-e29b-41d4-a716-************")
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), errors.ErrTodoNotFound, err)
}

// TestPostgresTodoRepository runs the test suite
func TestPostgresTodoRepository(t *testing.T) {
	suite.Run(t, new(PostgresTodoRepositoryTestSuite))
}

// getEnvOrDefault gets an environment variable or returns a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt gets an environment variable as integer with a default value
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsDuration gets an environment variable as duration with a default value
func getEnvAsDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
