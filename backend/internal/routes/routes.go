package routes

import (
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"

	"backend/internal/handlers"
)

// SetupRoutes configures all routes for the application
func SetupRoutes(e *echo.Echo, todoHandler *handlers.TodoHandler) {
	// API group with common middleware
	api := e.Group("/api/v1")
	
	// Add middleware for API routes
	api.Use(middleware.Logger())
	api.Use(middleware.Recover())
	api.Use(middleware.CORS())
	
	// Content-Type validation middleware for POST/PUT requests
	api.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			method := c.Request().Method
			if method == "POST" || method == "PUT" {
				contentType := c.Request().Header.Get("Content-Type")
				if contentType != "" && contentType != "application/json" {
					return echo.NewHTTPError(415, "Content-Type must be application/json")
				}
			}
			return next(c)
		}
	})

	// Todo routes
	todos := api.Group("/todos")
	todos.GET("", todoHandler.GetAllTodos)
	todos.POST("", todoHandler.CreateTodo)
	todos.GET("/:id", todoHandler.GetTodo)
	todos.PUT("/:id", todoHandler.UpdateTodo)
	todos.DELETE("/:id", todoHandler.DeleteTodo)

	// Health check endpoint
	api.GET("/health", func(c echo.Context) error {
		return c.JSON(200, map[string]string{
			"status": "ok",
			"service": "todo-api",
		})
	})
}
