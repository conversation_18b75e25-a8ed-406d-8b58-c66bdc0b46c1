package testutil

import (
	"context"
	"log"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/require"

	"backend/internal/config"
	"backend/internal/database"
	"backend/internal/migrations"
	"backend/internal/repository"
)

// SetupTestDB sets up a PostgreSQL test database and returns a repository
func SetupTestDB(t *testing.T) (repository.TodoRepository, func()) {
	return setupPostgresTestDB(t)
}

// setupPostgresTestDB sets up a PostgreSQL test database
func setupPostgresTestDB(t *testing.T) (repository.TodoRepository, func()) {
	// Load .env file for test configuration
	// Try multiple paths to find the .env file
	envPaths := []string{".env", "../.env", "../../.env", "../../../.env"}
	var envLoaded bool
	for _, path := range envPaths {
		if err := godotenv.Load(path); err == nil {
			envLoaded = true
			break
		}
	}
	if !envLoaded {
		log.Printf("Warning: Could not load .env file for tests from any of the paths: %v", envPaths)
	}

	cfg := &config.DatabaseConfig{
		Host:           getEnvOrDefault("DB_HOST", "localhost"),
		Port:           getEnvAsInt("DB_PORT", 5432),
		Name:           getEnvOrDefault("DB_NAME", "todo_test"),
		User:           getEnvOrDefault("DB_USER", "postgres"),
		Password:       getEnvOrDefault("DB_PASSWORD", ""),
		SSLMode:        getEnvOrDefault("DB_SSLMODE", "prefer"),
		MaxConns:       getEnvAsInt("DB_MAX_CONNS", 5),
		MinConns:       getEnvAsInt("DB_MIN_CONNS", 1),
		ConnectTimeout: getEnvAsDuration("DB_CONNECT_TIMEOUT", 5*time.Second),  // Shorter timeout for tests
		QueryTimeout:   getEnvAsDuration("DB_QUERY_TIMEOUT", 10*time.Second),  // Shorter timeout for tests
	}

	// Create database connection
	db, err := database.NewPostgresDB(cfg)
	require.NoError(t, err, "Failed to connect to test database")

	// Run migrations
	migrator := migrations.NewMigrator(db.Pool)
	err = migrator.Up(context.Background())
	require.NoError(t, err, "Failed to run migrations")

	// Create repository
	repo := repository.NewPostgresTodoRepository(db.Pool)

	// Return cleanup function
	cleanup := func() {
		// Clean up test data
		_, _ = db.Pool.Exec(context.Background(), "DELETE FROM todos")
		db.Close()
	}

	return repo, cleanup
}

// CleanupTestDB cleans up test data from the database
func CleanupTestDB(t *testing.T, db *database.PostgresDB) {
	_, err := db.Pool.Exec(context.Background(), "DELETE FROM todos")
	require.NoError(t, err, "Failed to clean up test data")
}

// getEnvOrDefault gets an environment variable or returns a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt gets an environment variable as integer with a default value
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsDuration gets an environment variable as duration with a default value
func getEnvAsDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
